{"folders": [{"name": "Syrix Main", "path": "."}], "settings": {"java.import.maven.enabled": true, "java.autobuild.enabled": true, "java.debug.settings.hotCodeReplace": "auto", "java.configuration.updateBuildConfiguration": "automatic", "java.maven.downloadSources": true, "java.compile.nullAnalysis.mode": "automatic", "java.workspace.filter": false, "java.project.resourceFilters": ["node_modules", ".git", "target", "build"], "java.debug.settings.onBuildFailureProceed": true, "java.debug.settings.enableRunDebugCodeLens": true, "files.exclude": {"**/node_modules": true, "**/target": true, "**/.git": true}, "[typescript]": {"editor.codeActionsOnSave": {"source.organizeImports": "never"}}}, "extensions": {"recommendations": ["vscjava.vscode-java-pack", "redhat.java", "vscjava.vscode-maven", "vscjava.vscode-java-debug"]}}